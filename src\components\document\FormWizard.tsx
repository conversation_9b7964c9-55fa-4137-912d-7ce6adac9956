import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Form } from '@/components/ui/form';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useDocuments } from '@/contexts/DocumentContext';
import { DocumentFormData, DOCUMENT_STATUSES, DOCUMENT_ACCESS_LEVELS } from '@/models/document';
import { ChevronLeft, ChevronRight, Save, Edit, Plus, FileText, Eye, CheckCircle2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { HACCPBreadcrumb, BreadcrumbPath } from '@/components/ui/breadcrumb';
import { ContextualHelp } from '@/components/ui/contextual-help';

// Step components
import BasicInfoStep from './FormSteps/BasicInfoStep';
import ApprovalStep from './FormSteps/ApprovalStep';
import FilesStep from './FormSteps/FilesStep';

const documentSchema = z.object({
  documentReferenceNumber: z.string().min(1, 'Document Reference Number is required'),
  documentTitle: z.string().min(1, 'Document Title is required'),
  version: z.number().min(0, 'Version must be a positive number'),
  issueDate: z.string().min(1, 'Issue Date is required'),
  authorFunction: z.string().min(1, 'Author Function is required'),
  responsibleDepartment: z.string().min(1, 'Responsible Department is required'),
  status: z.string().min(1, 'Status is required'),
  approvalDate: z.string().optional().or(z.literal('')),
  approvedByFunction: z.string().optional().or(z.literal('')),
  revisionDate: z.string().optional().or(z.literal('')),
  revisionReason: z.string().optional().or(z.literal('')),
  previousReferenceNumber: z.string().optional().or(z.literal('')),
  storageLocation: z.string().min(1, 'Storage Location is required'),
  distributionMethod: z.string().optional().or(z.literal('')),
  remarks: z.string().optional().or(z.literal('')),
  accessLevel: z.string().min(1, 'Access Level is required'),
});

interface FormWizardProps {
  className?: string;
}

const FormWizard: React.FC<FormWizardProps> = ({ className }) => {
  const { 
    addDocument, 
    updateDocument, 
    currentEditId, 
    setCurrentEditId, 
    documents,
    isLoading 
  } = useDocuments();
  
  const { toast } = useToast();
  const [currentStep, setCurrentStep] = useState(1);
  const [currentFile, setCurrentFile] = useState<File | null>(null);
  const [forceStep, setForceStep] = useState<number | null>(null);
  const [isAutoSaving, setIsAutoSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);

  const form = useForm<DocumentFormData>({
    resolver: zodResolver(documentSchema),
    defaultValues: {
      documentReferenceNumber: '',
      documentTitle: '',
      version: 1,
      issueDate: '',
      authorFunction: '',
      responsibleDepartment: '',
      status: '',
      storageLocation: '',
      accessLevel: '',
      approvalDate: '',
      approvedByFunction: '',
      revisionDate: '',
      revisionReason: '',
      previousReferenceNumber: '',
      distributionMethod: '',
      remarks: ''
    }
  });

  const steps = [
    {
      id: 1,
      title: 'Basic Information',
      description: 'Document details and identification',
      icon: <FileText className="h-4 w-4" />,
      fields: ['documentReferenceNumber', 'documentTitle', 'version', 'issueDate', 'authorFunction', 'responsibleDepartment', 'status', 'storageLocation', 'accessLevel'] as (keyof DocumentFormData)[]
    },
    {
      id: 2,
      title: 'Approval & Revision',
      description: 'Validation and revision details',
      icon: <CheckCircle2 className="h-4 w-4" />,
      fields: ['approvalDate', 'approvedByFunction', 'revisionDate', 'revisionReason', 'previousReferenceNumber', 'distributionMethod'] as (keyof DocumentFormData)[]
    },
    {
      id: 3,
      title: 'Files & Notes',
      description: 'File attachments and additional remarks',
      icon: <Plus className="h-4 w-4" />,
      fields: ['remarks'] as (keyof DocumentFormData)[]
    },
    {
      id: 4,
      title: 'Review & Submit',
      description: 'Review all information before submitting',
      icon: <Eye className="h-4 w-4" />,
      fields: [] as (keyof DocumentFormData)[]
    }
  ];

  // Auto-save functionality
  useEffect(() => {
    const autoSave = async () => {
      if (!form.formState.isDirty || form.formState.isSubmitting) return;

      setIsAutoSaving(true);
      try {
        // Save draft to localStorage
        const formData = form.getValues();
        const draftKey = currentEditId ? `document_draft_${currentEditId}` : 'document_draft_new';
        localStorage.setItem(draftKey, JSON.stringify({
          ...formData,
          currentStep,
          lastSaved: new Date().toISOString(),
          fileName: currentFile?.name || ''
        }));
        setLastSaved(new Date());
      } catch (error) {
        console.error('Auto-save failed:', error);
      } finally {
        setIsAutoSaving(false);
      }
    };

    const autoSaveTimer = setTimeout(autoSave, 2000); // Auto-save after 2 seconds of inactivity
    return () => clearTimeout(autoSaveTimer);
  }, [form.watch(), currentStep, currentFile, currentEditId]);

  // Load document data when editing
  React.useEffect(() => {
    if (currentEditId) {
      const document = documents.find(doc => doc.id === currentEditId);
      if (document) {
        form.reset({
          documentReferenceNumber: document.documentReferenceNumber,
          documentTitle: document.documentTitle,
          version: document.version,
          issueDate: document.issueDate,
          authorFunction: document.authorFunction,
          responsibleDepartment: document.responsibleDepartment,
          status: document.status,
          storageLocation: document.storageLocation,
          accessLevel: document.accessLevel,
          approvalDate: document.approvalDate || '',
          approvedByFunction: document.approvedByFunction || '',
          revisionDate: document.revisionDate || '',
          revisionReason: document.revisionReason || '',
          previousReferenceNumber: document.previousReferenceNumber || '',
          distributionMethod: document.distributionMethod || '',
          remarks: document.remarks || ''
        });
      }
    }
  }, [currentEditId, documents, form]);

  const handleNext = () => {
    if (currentStep < steps.length) {
      setCurrentStep(prev => prev + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(prev => prev - 1);
    }
  };

  const handleStepClick = (stepId: number) => {
    setCurrentStep(stepId);
  };

  // Keyboard navigation support
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Only handle keyboard navigation when not in an input field
      if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {
        return;
      }

      switch (e.key) {
        case 'ArrowLeft':
          if (currentStep > 1) {
            e.preventDefault();
            setCurrentStep(currentStep - 1);
          }
          break;
        case 'ArrowRight':
          if (currentStep < steps.length) {
            e.preventDefault();
            setCurrentStep(currentStep + 1);
          }
          break;
        case 'Enter':
          if (e.ctrlKey || e.metaKey) {
            e.preventDefault();
            form.handleSubmit(onSubmit)();
          }
          break;
        case 'Escape':
          e.preventDefault();
          handleClear();
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [currentStep, steps.length, form]);

  const onSubmit = async (data: DocumentFormData) => {
    try {
      const documentData = {
        documentReferenceNumber: data.documentReferenceNumber,
        documentTitle: data.documentTitle,
        version: data.version,
        issueDate: data.issueDate,
        authorFunction: data.authorFunction,
        responsibleDepartment: data.responsibleDepartment,
        status: data.status === '' ? DOCUMENT_STATUSES[0] : data.status,
        approvalDate: data.approvalDate,
        approvedByFunction: data.approvedByFunction,
        revisionDate: data.revisionDate,
        revisionReason: data.revisionReason,
        previousReferenceNumber: data.previousReferenceNumber,
        storageLocation: data.storageLocation,
        distributionMethod: data.distributionMethod,
        remarks: data.remarks,
        accessLevel: data.accessLevel === '' ? DOCUMENT_ACCESS_LEVELS[0] : data.accessLevel,
        fileName: currentFile?.name || '',
        fileData: currentFile ? URL.createObjectURL(currentFile) : '',
      };

      if (currentEditId) {
        updateDocument(currentEditId, documentData);
      } else {
        addDocument(documentData);
      }

      handleClear();
    } catch (error) {
      console.error('Error saving document:', error);
      toast({
        title: "Error",
        description: "Failed to save document. Please check the form data and try again.",
        variant: "destructive",
      });
    }
  };

  const handleClear = () => {
    form.reset();
    setCurrentEditId(null);
    setCurrentFile(null);
    setCurrentStep(1);
    setForceStep(null);
  };

  const progress = (currentStep / steps.length) * 100;
  const displayStep = forceStep || currentStep;

  // Breadcrumb paths
  const breadcrumbPaths: BreadcrumbPath[] = [
    {
      label: 'Document Management',
      to: '/document-management',
      icon: <FileText className="h-4 w-4" />
    },
    {
      label: currentEditId ? 'Edit Document' : 'Add New Document',
      icon: currentEditId ? <Edit className="h-4 w-4" /> : <Plus className="h-4 w-4" />
    },
    {
      label: steps[displayStep - 1]?.title || 'Step',
      icon: steps[displayStep - 1]?.icon
    }
  ];

  const renderCurrentStep = () => {
    switch (displayStep) {
      case 1:
        return <BasicInfoStep form={form} />;
      case 2:
        return <ApprovalStep form={form} />;
      case 3:
        return (
          <FilesStep
            form={form}
            currentFile={currentFile}
            onFileChange={setCurrentFile}
          />
        );
      case 4:
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-4">Review & Submit</h3>
              <p className="text-sm text-muted-foreground mb-6">
                Please review all the information below before submitting the document.
              </p>
            </div>

            {/* Summary of all form data */}
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h4 className="text-md font-medium text-primary">Basic Information</h4>
                  <div className="space-y-2 text-sm">
                    <div><strong>Reference:</strong> {form.watch('documentReferenceNumber') || 'Not specified'}</div>
                    <div><strong>Title:</strong> {form.watch('documentTitle') || 'Not specified'}</div>
                    <div><strong>Version:</strong> {form.watch('version') || 'Not specified'}</div>
                    <div><strong>Issue Date:</strong> {form.watch('issueDate') || 'Not specified'}</div>
                    <div><strong>Author Function:</strong> {form.watch('authorFunction') || 'Not specified'}</div>
                    <div><strong>Department:</strong> {form.watch('responsibleDepartment') || 'Not specified'}</div>
                    <div><strong>Status:</strong> {form.watch('status') || 'Not specified'}</div>
                    <div><strong>Storage Location:</strong> {form.watch('storageLocation') || 'Not specified'}</div>
                    <div><strong>Access Level:</strong> {form.watch('accessLevel') || 'Not specified'}</div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="text-md font-medium text-primary">Approval & Revision</h4>
                  <div className="space-y-2 text-sm">
                    <div><strong>Validation Date:</strong> {form.watch('approvalDate') || 'Not specified'}</div>
                    <div><strong>Validated By:</strong> {form.watch('approvedByFunction') || 'Not specified'}</div>
                    <div><strong>Revision Date:</strong> {form.watch('revisionDate') || 'Not specified'}</div>
                    <div><strong>Previous Reference:</strong> {form.watch('previousReferenceNumber') || 'Not specified'}</div>
                    <div><strong>Distribution Method:</strong> {form.watch('distributionMethod') || 'Not specified'}</div>
                  </div>
                </div>
              </div>

              {form.watch('revisionReason') && (
                <div className="space-y-2">
                  <h4 className="text-md font-medium text-primary">Revision Reason</h4>
                  <p className="text-sm bg-muted p-3 rounded-md">{form.watch('revisionReason')}</p>
                </div>
              )}

              {form.watch('remarks') && (
                <div className="space-y-2">
                  <h4 className="text-md font-medium text-primary">Additional Remarks</h4>
                  <p className="text-sm bg-muted p-3 rounded-md">{form.watch('remarks')}</p>
                </div>
              )}

              {currentFile && (
                <div className="space-y-2">
                  <h4 className="text-md font-medium text-primary">Attached File</h4>
                  <div className="flex items-center gap-2 text-sm bg-muted p-3 rounded-md">
                    <FileText className="h-4 w-4" />
                    <span>{currentFile.name}</span>
                    <span className="text-muted-foreground">({(currentFile.size / 1024 / 1024).toFixed(2)} MB)</span>
                  </div>
                </div>
              )}
            </div>
          </div>
        );
      default:
        return <BasicInfoStep form={form} />;
    }
  };

  return (
    <div className={className}>
      {/* Breadcrumb Navigation */}
      <div className="mb-6">
        <HACCPBreadcrumb paths={breadcrumbPaths} />
      </div>

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              {currentEditId ? (
                <>
                  <Edit className="h-5 w-5" />
                  Edit Document
                </>
              ) : (
                <>
                  <Plus className="h-5 w-5" />
                  Add New Document
                </>
              )}
            </CardTitle>

            {/* Help System Integration */}
            <ContextualHelp topic="document-management" />
          </div>

          {/* Auto-save indicator */}
          {isAutoSaving && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <div className="animate-spin h-3 w-3 border border-primary border-t-transparent rounded-full"></div>
              Saving draft...
            </div>
          )}

          {lastSaved && !isAutoSaving && (
            <div className="text-sm text-muted-foreground">
              Last saved: {lastSaved.toLocaleTimeString()}
            </div>
          )}
        
        {/* Progress Indicator */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm text-muted-foreground">
            <span>Step {displayStep} of {steps.length}</span>
            <span>{Math.round(progress)}% Complete</span>
          </div>
          <Progress value={progress} className="h-2" />
          
          {/* Step Labels with Enhanced Visual Cues */}
          <div className="flex justify-between">
            {steps.map((step, index) => {
              const isActive = displayStep === step.id;
              const isCompleted = currentStep > step.id;
              const isClickable = step.id <= currentStep || forceStep !== null;

              return (
                <div
                  key={step.id}
                  className={`text-center flex-1 ${index < steps.length - 1 ? 'border-r border-muted' : ''}`}
                >
                  <button
                    type="button"
                    onClick={() => handleStepClick(step.id)}
                    disabled={!isClickable}
                    className={`w-full p-3 rounded-md transition-all duration-200 group relative ${
                      isClickable
                        ? 'hover:bg-muted/50 cursor-pointer hover:shadow-sm'
                        : 'cursor-not-allowed opacity-50'
                    } ${
                      isActive
                        ? 'bg-primary/10 border border-primary/20'
                        : isCompleted
                        ? 'bg-green-50 border border-green-200'
                        : 'border border-transparent'
                    }`}
                    aria-label={`Go to ${step.title} step`}
                    aria-current={isActive ? 'step' : undefined}
                  >
                    {/* Step Icon and Number */}
                    <div className="flex items-center justify-center mb-2">
                      <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 transition-colors ${
                        isActive
                          ? 'bg-primary text-primary-foreground border-primary'
                          : isCompleted
                          ? 'bg-green-600 text-white border-green-600'
                          : 'bg-muted text-muted-foreground border-muted-foreground/30'
                      }`}>
                        {isCompleted ? (
                          <CheckCircle2 className="h-4 w-4" />
                        ) : (
                          <span className="text-xs font-medium">{step.id}</span>
                        )}
                      </div>
                    </div>

                    {/* Step Title and Description */}
                    <div className={`text-sm font-medium mb-1 ${
                      isActive
                        ? 'text-primary'
                        : isCompleted
                        ? 'text-green-700'
                        : 'text-muted-foreground'
                    }`}>
                      {step.title}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {step.description}
                    </div>

                    {/* Click indicator */}
                    {isClickable && !isActive && (
                      <div className="absolute inset-0 rounded-md border-2 border-transparent group-hover:border-primary/30 transition-colors" />
                    )}
                  </button>
                </div>
              );
            })}
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Current Step Content */}
            {renderCurrentStep()}

            {/* Navigation Buttons */}
            <div className="flex justify-between">
              <div>
                {displayStep > 1 && (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handlePrevious}
                    disabled={isLoading}
                  >
                    <ChevronLeft className="h-4 w-4 mr-2" />
                    Previous
                  </Button>
                )}
              </div>

              <div className="flex gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleClear}
                  disabled={isLoading}
                >
                  Clear
                </Button>

                {displayStep < steps.length ? (
                  <Button
                    type="button"
                    onClick={handleNext}
                    disabled={isLoading}
                  >
                    Next
                    <ChevronRight className="h-4 w-4 ml-2" />
                  </Button>
                ) : (
                  <Button
                    type="submit"
                    disabled={isLoading}
                  >
                    <Save className="h-4 w-4 mr-2" />
                    {currentEditId ? 'Update Document' : 'Save Document'}
                  </Button>
                )}
              </div>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
    </div>
  );
};

export default FormWizard;