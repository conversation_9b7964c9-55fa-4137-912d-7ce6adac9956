import React from 'react';
import { UseFormReturn } from 'react-hook-form';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Textarea } from '@/components/ui/textarea';
import { DocumentFormData } from '@/models/document';
import FileUpload from '../FileUpload';

interface FilesStepProps {
  form: UseFormReturn<DocumentFormData>;
  currentFile: File | null;
  onFileChange: (file: File | null) => void;
}

const FilesStep: React.FC<FilesStepProps> = ({ form, currentFile, onFileChange }) => {
  return (
    <div className="space-y-6" role="tabpanel" aria-labelledby="files-tab">
      <div>
        <h3 id="files-heading" className="text-lg font-medium mb-4">Files & Additional Notes</h3>
        <p className="text-sm text-muted-foreground mb-6">
          Upload document files and add any additional remarks or notes. File upload is optional.
        </p>
      </div>

      {/* File Upload Section */}
      <div className="space-y-4">
        <h4 className="text-md font-medium text-primary">File Attachment</h4>
        <FileUpload
          currentFile={currentFile}
          onFileChange={onFileChange}
        />
      </div>

      {/* Remarks Section */}
      <div className="space-y-4">
        <h4 className="text-md font-medium text-primary">Additional Information</h4>
        <FormField
          control={form.control}
          name="remarks"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Remarks</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Enter any additional remarks, notes, or special instructions..."
                  className="min-h-[120px]"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      {/* Summary Section */}
      <div className="space-y-4">
        <h4 className="text-md font-medium text-primary">Document Summary</h4>
        <div className="p-4 bg-muted rounded-lg space-y-2">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium">Reference:</span>
              <span className="ml-2">{form.watch('documentReferenceNumber') || 'Not set'}</span>
            </div>
            <div>
              <span className="font-medium">Version:</span>
              <span className="ml-2">v{form.watch('version') || '0'}</span>
            </div>
            <div>
              <span className="font-medium">Status:</span>
              <span className="ml-2">{form.watch('status') || 'Not set'}</span>
            </div>
            <div>
              <span className="font-medium">Access Level:</span>
              <span className="ml-2">{form.watch('accessLevel') || 'Not set'}</span>
            </div>
          </div>
          <div className="pt-2 border-t border-muted-foreground/20">
            <div className="text-sm">
              <span className="font-medium">Title:</span>
              <span className="ml-2">{form.watch('documentTitle') || 'Not set'}</span>
            </div>
          </div>
          {currentFile && (
            <div className="pt-2 border-t border-muted-foreground/20">
              <div className="text-sm">
                <span className="font-medium">Attached File:</span>
                <span className="ml-2">{currentFile.name}</span>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default FilesStep;
